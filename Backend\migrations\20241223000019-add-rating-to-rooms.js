'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableInfo = await queryInterface.describeTable('rooms');
    
    // Add average_rating column if not exists
    if (!tableInfo.average_rating) {
      await queryInterface.addColumn('rooms', 'average_rating', {
        type: Sequelize.DECIMAL(3, 2),
        allowNull: true,
        defaultValue: null,
        comment: 'Average rating from 1.00 to 5.00'
      });
    }

    // Add total_ratings column if not exists
    if (!tableInfo.total_ratings) {
      await queryInterface.addColumn('rooms', 'total_ratings', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Total number of ratings'
      });
    }

    // Add total_views column if not exists
    if (!tableInfo.total_views) {
      await queryInterface.addColumn('rooms', 'total_views', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Total number of views'
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('rooms', 'average_rating');
    await queryInterface.removeColumn('rooms', 'total_ratings');
    await queryInterface.removeColumn('rooms', 'total_views');
  }
};
