import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Row,
  Col,
  Typography,
  message,
  Rate,
  Upload,
  Space
} from 'antd';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  StarOutlined,
  FileImageOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import Layout from '../components/layout/Layout';
import HtmlEditor from '../components/common/HtmlEditor';
import { placeAPI, reviewAPI, addressAPI, getUploadUrl } from '../services/api';

const { Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const CreateReview = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [places, setPlaces] = useState([]);
  const [fileList, setFileList] = useState([]); // Ảnh avatar bài viết
  const [content, setContent] = useState(''); // Nội dung HTML
  const [featuredImage, setFeaturedImage] = useState(null); // Ảnh đại diện

  // Address states
  const [provinces, setProvinces] = useState([]);
  const [districts, setDistricts] = useState([]);
  const [wards, setWards] = useState([]);
  const [selectedProvince, setSelectedProvince] = useState('');
  const [selectedDistrict, setSelectedDistrict] = useState('');
  const [selectedWard, setSelectedWard] = useState('');

  useEffect(() => {
    fetchPlaces();
    fetchProvinces();

    // Set initial place if passed from navigation
    if (location.state?.placeId) {
      form.setFieldsValue({ place_id: location.state.placeId });
    }
  }, [location.state]);

  // Fetch provinces
  const fetchProvinces = async () => {
    try {
      const response = await addressAPI.getProvinces();
      if (response.success) {
        setProvinces(response.data.provinces);
      }
    } catch (error) {
      console.error('Error fetching provinces:', error);
    }
  };

  // Fetch districts
  const fetchDistricts = async (provinceId) => {
    try {
      const response = await addressAPI.getDistricts(provinceId);
      if (response.success) {
        setDistricts(response.data.districts);
      }
    } catch (error) {
      console.error('Error fetching districts:', error);
    }
  };

  // Fetch wards
  const fetchWards = async (districtId) => {
    try {
      const response = await addressAPI.getWards(districtId);
      if (response.success) {
        setWards(response.data.wards);
      }
    } catch (error) {
      console.error('Error fetching wards:', error);
    }
  };

  // Handle location changes
  const handleProvinceChange = (value) => {
    setSelectedProvince(value);
    setSelectedDistrict('');
    setSelectedWard('');
    setDistricts([]);
    setWards([]);
    if (value) {
      fetchDistricts(value);
    }
    form.setFieldsValue({ district_id: undefined, ward_id: undefined });
  };

  const handleDistrictChange = (value) => {
    setSelectedDistrict(value);
    setSelectedWard('');
    setWards([]);
    if (value) {
      fetchWards(value);
    }
    form.setFieldsValue({ ward_id: undefined });
  };

  const handleWardChange = (value) => {
    setSelectedWard(value);
  };

  // Thể loại địa điểm du lịch
  const travelCategories = [
    { value: 'dining', label: 'Ăn uống' },
    { value: 'mountain', label: 'Leo núi' },
    { value: 'resort', label: 'Nghỉ dưỡng' },
    { value: 'beach', label: 'Biển' },
    { value: 'culture', label: 'Văn hóa' },
    { value: 'adventure', label: 'Phiêu lưu' },
    { value: 'shopping', label: 'Mua sắm' },
    { value: 'nightlife', label: 'Giải trí đêm' },
    { value: 'nature', label: 'Thiên nhiên' },
    { value: 'historical', label: 'Lịch sử' },
    { value: 'spiritual', label: 'Tâm linh' },
    { value: 'festival', label: 'Lễ hội' }
  ];

  const fetchPlaces = async () => {
    try {
      const response = await placeAPI.getAllPlaces();
      if (response.success) {
        setPlaces(response.data.places);
      }
    } catch (error) {
      console.error('Error fetching places:', error);
      message.error('Không thể tải danh sách địa điểm');
    }
  };

  const handleSubmit = async (values) => {
    setLoading(true);
    
    try {
      // Validate required fields
      if (!content || content.replace(/<[^>]*>/g, '').trim().length < 50) {
        message.error('Nội dung phải có ít nhất 50 ký tự');
        setLoading(false);
        return;
      }

      // Prepare review data
      const reviewData = {
        title: values.title,
        content: content, // HTML content from ReactQuill
        rating: values.rating,
        category: values.category, // Thể loại địa điểm
        province_id: values.province_id,
        district_id: values.district_id,
        ward_id: values.ward_id,
        location_name: values.location_name, // Tên địa điểm cụ thể
        featured_image: featuredImage?.url || featuredImage?.response?.data?.files?.[0]?.url,
        images: fileList
          .filter(file => file.status === 'done')
          .map(file => ({
            url: file.url || file.response?.data?.files?.[0]?.url,
            caption: file.name || 'Review image'
          })),
        status: 'pending' // Mặc định là pending, cần admin duyệt
      };

      console.log('📤 Sending review data:', reviewData);
      console.log('🔑 Auth token:', localStorage.getItem('token') ? 'Present' : 'Missing');

      const response = await reviewAPI.createReview(reviewData);

      console.log('📥 API response:', response);

      if (response.success) {
        message.success('Đăng review thành công! Bài viết đang chờ duyệt.');
        navigate('/my-reviews'); // Redirect to my reviews page
      } else {
        message.error(response.message || 'Có lỗi xảy ra khi đăng review');
      }
    } catch (error) {
      console.error('Error creating review:', error);
      message.error(error.message || 'Có lỗi xảy ra khi đăng review');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadChange = ({ file, fileList: newFileList }) => {
    setFileList(newFileList);

    if (file.status === 'done') {
      message.success(`${file.name} đã upload thành công`);
    } else if (file.status === 'error') {
      message.error(`${file.name} upload thất bại`);
    }
  };

  const handleFeaturedImageChange = ({ file }) => {
    if (file.status === 'done') {
      setFeaturedImage(file);
      message.success('Ảnh đại diện đã upload thành công');
    } else if (file.status === 'error') {
      message.error('Upload ảnh đại diện thất bại');
    } else if (file.status === 'uploading') {
      setFeaturedImage(file);
    }
  };



  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Tải ảnh lên</div>
    </div>
  );

  const featuredImageUploadButton = (
    <div>
      <FileImageOutlined />
      <div style={{ marginTop: 8 }}>Ảnh đại diện</div>
    </div>
  );

  return (
    <Layout>
      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate(-1)}
            style={{ marginBottom: '16px' }}
          >
            Quay lại
          </Button>
          <Title level={2}>
            <StarOutlined style={{ marginRight: '8px', color: '#faad14' }} />
            Đăng review địa điểm du lịch
          </Title>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          size="large"
        >
          <Row gutter={[24, 16]}>
            {/* Thể loại địa điểm */}
            <Col xs={24} md={12}>
              <Form.Item
                label="Thể loại địa điểm"
                name="category"
                rules={[{ required: true, message: 'Vui lòng chọn thể loại!' }]}
              >
                <Select
                  placeholder="Chọn thể loại địa điểm du lịch..."
                  size="large"
                >
                  {travelCategories.map(category => (
                    <Option key={category.value} value={category.value}>
                      {category.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* Tỉnh/Thành phố */}
            <Col xs={24} md={8}>
              <Form.Item
                label="Tỉnh/Thành phố"
                name="province_id"
                rules={[{ required: true, message: 'Vui lòng chọn tỉnh/thành phố!' }]}
              >
                <Select
                  placeholder="Chọn tỉnh/thành phố"
                  size="large"
                  onChange={handleProvinceChange}
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {provinces.map(province => (
                    <Option key={province.id} value={province.id}>
                      {province.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* Quận/Huyện */}
            <Col xs={24} md={8}>
              <Form.Item
                label="Quận/Huyện"
                name="district_id"
                rules={[{ required: true, message: 'Vui lòng chọn quận/huyện!' }]}
              >
                <Select
                  placeholder="Chọn quận/huyện"
                  size="large"
                  onChange={handleDistrictChange}
                  disabled={!selectedProvince}
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {districts.map(district => (
                    <Option key={district.id} value={district.id}>
                      {district.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* Phường/Xã */}
            <Col xs={24} md={8}>
              <Form.Item
                label="Phường/Xã"
                name="ward_id"
                rules={[{ required: true, message: 'Vui lòng chọn phường/xã!' }]}
              >
                <Select
                  placeholder="Chọn phường/xã"
                  size="large"
                  onChange={handleWardChange}
                  disabled={!selectedDistrict}
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {wards.map(ward => (
                    <Option key={ward.id} value={ward.id}>
                      {ward.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* Tên địa điểm cụ thể */}
            <Col xs={24}>
              <Form.Item
                label="Tên địa điểm cụ thể"
                name="location_name"
                rules={[{ required: true, message: 'Vui lòng nhập tên địa điểm!' }]}
              >
                <Input
                  placeholder="Ví dụ: Nhà hàng ABC, Khách sạn XYZ, Chùa Một Cột..."
                  size="large"
                />
              </Form.Item>
            </Col>

            {/* Tiêu đề */}
            <Col xs={24} md={12}>
              <Form.Item
                label="Tiêu đề bài viết"
                name="title"
                rules={[
                  { required: true, message: 'Vui lòng nhập tiêu đề!' },
                  { min: 10, message: 'Tiêu đề phải có ít nhất 10 ký tự!' }
                ]}
              >
                <Input
                  placeholder="Nhập tiêu đề hấp dẫn cho bài review..."
                  size="large"
                />
              </Form.Item>
            </Col>

            {/* Đánh giá */}
            <Col xs={24} md={12}>
              <Form.Item
                label="Đánh giá"
                name="rating"
                rules={[{ required: true, message: 'Vui lòng chọn số sao!' }]}
              >
                <Rate
                  style={{ fontSize: '24px' }}
                  character={<StarOutlined />}
                />
              </Form.Item>
            </Col>



            <Col xs={24}>
              <Form.Item
                label="Ảnh đại diện bài viết"
                help="Ảnh này sẽ hiển thị làm thumbnail cho bài viết của bạn"
              >
                <Upload
                  listType="picture-card"
                  fileList={featuredImage ? [featuredImage] : []}
                  onChange={handleFeaturedImageChange}
                  action={getUploadUrl('images')}
                  headers={{
                    Authorization: `Bearer ${localStorage.getItem('token') || ''}`
                  }}
                  name="images"
                  accept="image/*"
                  maxCount={1}
                  beforeUpload={(file) => {
                    const isImage = file.type.startsWith('image/');
                    if (!isImage) {
                      message.error('Chỉ có thể upload file ảnh!');
                      return false;
                    }
                    const isLt5M = file.size / 1024 / 1024 < 5;
                    if (!isLt5M) {
                      message.error('Kích thước ảnh phải nhỏ hơn 5MB!');
                      return false;
                    }
                    return true;
                  }}
                >
                  {!featuredImage ? featuredImageUploadButton : null}
                </Upload>
              </Form.Item>
            </Col>

            <Col xs={24}>
              <Form.Item
                name="content"
                label="Nội dung review"
                rules={[
                  {
                    validator: () => {
                      // Use the actual content state instead of form value
                      const currentContent = content || '';
                      const textContent = currentContent.replace(/<[^>]*>/g, '').trim();

                      if (textContent.length < 50) {
                        return Promise.reject(new Error('Nội dung phải có ít nhất 50 ký tự!'));
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
              >
                <HtmlEditor
                  value={content}
                  onChange={(value) => {
                    setContent(value);
                    // Update form field value without triggering validation immediately
                    form.setFieldsValue({ content: value });
                  }}
                  placeholder="Chia sẻ trải nghiệm chi tiết của bạn về địa điểm này... Bạn có thể thêm ảnh, định dạng text và tạo danh sách."
                />
              </Form.Item>
            </Col>

            {/* <Col xs={24}>
              <Form.Item
                label="Ảnh bổ sung (tùy chọn)"
                help="Những ảnh này sẽ được đính kèm với bài viết, khác với ảnh trong nội dung"
              >
                <Upload
                  listType="picture-card"
                  fileList={fileList}
                  onChange={handleUploadChange}
                  action={getUploadUrl('images')}
                  headers={{
                    Authorization: `Bearer ${localStorage.getItem('token') || ''}`
                  }}
                  name="images"
                  multiple
                  accept="image/*"
                  beforeUpload={(file) => {
                    const isImage = file.type.startsWith('image/');
                    if (!isImage) {
                      message.error('Chỉ có thể upload file ảnh!');
                      return false;
                    }
                    const isLt5M = file.size / 1024 / 1024 < 5;
                    if (!isLt5M) {
                      message.error('Kích thước ảnh phải nhỏ hơn 5MB!');
                      return false;
                    }
                    return true;
                  }}
                >
                  {fileList.length >= 5 ? null : uploadButton}
                </Upload>
                <div style={{ marginTop: '8px', color: '#666', fontSize: '12px' }}>
                  📸 Tải lên tối đa 5 ảnh bổ sung. Định dạng: JPG, PNG. Kích thước tối đa: 5MB/ảnh.
                </div>
              </Form.Item>
            </Col> */}

            <Col xs={24}>
              <Form.Item style={{ textAlign: 'center', marginTop: '32px' }}>
                <Space size="middle">
                  <Button size="large" onClick={() => navigate(-1)}>
                    Hủy
                  </Button>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={loading}
                    size="large"
                  >
                    Đăng review
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>
    </Layout>
  );
};

export default CreateReview;
