import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Space, Dropdown, Avatar, message, Input } from 'antd';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { getServerBaseUrl } from '../../utils/apiConfig';
import './Header.css';
import {
  HomeOutlined,
  PlusOutlined,
  UserOutlined,
  LoginOutlined,
  LogoutOutlined,
  EnvironmentOutlined,
  SettingOutlined,
  StarOutlined,
  GlobalOutlined,
  InfoCircleOutlined,
  SearchOutlined
} from '@ant-design/icons';

const { Header: AntHeader } = Layout;

const Header = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, isAdmin } = useAuth();
  const [searchValue, setSearchValue] = useState('');

  // Debug user context in header
  console.log('🔍 Header - Current user:', user);
  console.log('🖼️ Header - Avatar URL:', user?.avatar_url);

  const handleLogout = () => {
    logout();
    message.success('Đăng xuất thành công!');
    navigate('/');
  };

  const handleSearch = () => {
    if (searchValue.trim()) {
      // Navigate to rooms page with search term
      navigate(`/rooms?search=${encodeURIComponent(searchValue)}`);
      setSearchValue(''); // Clear search after navigation
    }
  };

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link to="/">Trang chủ</Link>,
    },
    {
      key: '/places',
      icon: <EnvironmentOutlined />,
      label: <Link to="/places">Địa điểm</Link>,
    },
    {
      key: '/reviews',
      icon: <StarOutlined />,
      label: <Link to="/reviews">Bài viết</Link>,
    },
    {
      key: '/rooms',
      icon: <HomeOutlined />,
      label: <Link to="/rooms">Phòng trọ</Link>,
    },
    {
      key: '/create-room',
      icon: <PlusOutlined />,
      label: <Link to="/create-room">Đăng phòng</Link>,
    },
    // Add My Rooms menu item if user is logged in
    ...(user ? [{
      key: '/my-rooms',
      icon: <HomeOutlined />,
      label: <Link to="/my-rooms">Phòng của tôi</Link>,
    }] : []),
    // Add admin menu item if user is admin
    ...(isAdmin() ? [{
      key: '/admin',
      icon: <SettingOutlined />,
      label: <Link to="/admin">Quản trị</Link>,
    }] : []),
  ];

  // Check if we're on the home page to apply transparent header
  const isHomePage = false; // Always use normal header style

  return (
    <AntHeader className="tripadvisor-header" style={{
      background: isHomePage ? 'transparent' : '#fff',
      borderBottom: isHomePage ? 'none' : '1px solid #e0e0e0',
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      zIndex: 1000,
      boxShadow: isHomePage ? 'none' : '0 2px 8px rgba(0, 0, 0, 0.1)',
      height: 'auto',
      transition: 'all 0.3s ease'
    }}>
      <div style={{
        maxWidth: '1600px',
        margin: '0 auto',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 24px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            fontSize: '24px',
            fontWeight: 'bold',
            color: isHomePage ? 'white' : '#00aa6c',
            cursor: 'pointer',
            textDecoration: 'none',
            marginRight: '48px',
            transition: 'all 0.3s ease'
          }} onClick={() => navigate('/')}>
            <div style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              background: isHomePage ? 'rgba(255, 255, 255, 0.2)' : '#00aa6c',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '8px',
              color: 'white',
              fontSize: '16px',
              fontWeight: 'bold',
              border: isHomePage ? '1px solid rgba(255, 255, 255, 0.3)' : 'none',
              transition: 'all 0.3s ease'
            }}>
              DL
            </div>
            <span>Du Lịch Việt</span>
          </div>

          <nav style={{ display: 'flex', gap: '32px' }}>
            <Link
              to="/places"
              style={{
                color: location.pathname === '/places' ? '#00aa6c' : '#333',
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                padding: '8px 0',
                borderBottom: location.pathname === '/places' ? '2px solid #00aa6c' : '2px solid transparent',
                transition: 'all 0.3s ease'
              }}
            >
              Khám phá
            </Link>
            <Link
              to="/reviews"
              style={{
                color: location.pathname === '/reviews' ? '#00aa6c' : (isHomePage ? 'white' : '#333'),
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                padding: '8px 0',
                borderBottom: location.pathname === '/reviews' ? '2px solid #00aa6c' : '2px solid transparent',
                transition: 'all 0.3s ease'
              }}
            >
              Bài viết
            </Link>
            <Link
              to="/rooms"
              style={{
                color: location.pathname === '/rooms' ? '#00aa6c' : (isHomePage ? 'white' : '#333'),
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                padding: '8px 0',
                borderBottom: location.pathname === '/rooms' ? '2px solid #00aa6c' : '2px solid transparent',
                transition: 'all 0.3s ease'
              }}
            >
              Phòng thuê
            </Link>
            <Link
              to="/about"
              style={{
                color: location.pathname === '/about' ? '#00aa6c' : (isHomePage ? 'white' : '#333'),
                textDecoration: 'none',
                fontWeight: '500',
                fontSize: '16px',
                padding: '8px 0',
                borderBottom: location.pathname === '/about' ? '2px solid #00aa6c' : '2px solid transparent',
                transition: 'all 0.3s ease'
              }}
            >
              Giới thiệu
            </Link>
            {user && (
              <Link
                to="/my-rooms"
                style={{
                  color: location.pathname === '/my-rooms' ? '#00aa6c' : (isHomePage ? 'white' : '#333'),
                  textDecoration: 'none',
                  fontWeight: '500',
                  fontSize: '16px',
                  padding: '8px 0',
                  borderBottom: location.pathname === '/my-rooms' ? '2px solid #00aa6c' : '2px solid transparent',
                  transition: 'all 0.3s ease'
                }}
              >
                Phòng của tôi
              </Link>
            )}
            {isAdmin() && (
              <Link
                to="/admin"
                style={{
                  color: location.pathname.startsWith('/admin') ? '#00aa6c' : (isHomePage ? 'white' : '#333'),
                  textDecoration: 'none',
                  fontWeight: '500',
                  fontSize: '16px',
                  padding: '8px 0',
                  borderBottom: location.pathname.startsWith('/admin') ? '2px solid #00aa6c' : '2px solid transparent',
                  transition: 'all 0.3s ease'
                }}
              >
                Quản trị
              </Link>
            )}
          </nav>
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {/* Header Search - show on all pages */}
          {true && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <Input
                placeholder="Tìm kiếm địa điểm, phòng..."
                prefix={<SearchOutlined style={{ color: '#999', fontSize: '16px' }} />}
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onPressEnter={handleSearch}
                style={{
                  width: '280px',
                  height: '42px',
                  borderRadius: '25px',
                  border: '1px solid #e0e0e0',
                  fontSize: '15px',
                  paddingLeft: '20px',
                  paddingRight: '20px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease'
                }}
                size="large"
                onFocus={(e) => {
                  e.target.style.boxShadow = '0 4px 12px rgba(0, 170, 108, 0.25)';
                  e.target.style.borderColor = '#00aa6c';
                }}
                onBlur={(e) => {
                  e.target.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
                  e.target.style.borderColor = '#e0e0e0';
                }}
              />
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
                style={{
                  height: '42px',
                  borderRadius: '25px',
                  background: '#00aa6c',
                  borderColor: '#00aa6c',
                  paddingLeft: '16px',
                  paddingRight: '16px',
                  boxShadow: '0 2px 8px rgba(0, 170, 108, 0.2)'
                }}
                size="large"
              />
            </div>
          )}
          {user ? (
            // User is logged in
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'profile',
                    icon: <UserOutlined />,
                    label: 'Thông tin cá nhân',
                    onClick: () => navigate('/profile')
                  },

                  {
                    key: 'my-rooms',
                    icon: <HomeOutlined />,
                    label: 'Phòng của tôi',
                    onClick: () => navigate('/my-rooms')
                  },
                  {
                    key: 'my-reviews',
                    icon: <StarOutlined />,
                    label: 'Bài viết của tôi',
                    onClick: () => navigate('/my-reviews')
                  },
                  {
                    key: 'create-room',
                    icon: <PlusOutlined />,
                    label: 'Đăng phòng',
                    onClick: () => navigate('/create-room')
                  },
                  
                  {
                    type: 'divider'
                  },
                  {
                    key: 'logout',
                    icon: <LogoutOutlined />,
                    label: 'Đăng xuất',
                    onClick: handleLogout
                  }
                ]
              }}
              placement="bottomRight"
            >
              <Button type="text" style={{
                color: isHomePage ? 'white' : '#333',
                height: 'auto',
                padding: '4px 8px',
                borderRadius: '20px',
                border: isHomePage ? '1px solid rgba(255, 255, 255, 0.3)' : 'none',
                boxShadow: 'none',
                background: isHomePage ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
                transition: 'all 0.3s ease'
              }}
              className="user-dropdown-btn"
              >
                <Space>
                  <Avatar
                    size="small"
                    src={user.avatar_url ? (
                      user.avatar_url.startsWith('http')
                        ? `${user.avatar_url}?t=${Date.now()}`
                        : `${getServerBaseUrl()}${user.avatar_url}?t=${Date.now()}`
                    ) : null}
                    icon={!user.avatar_url && <UserOutlined />}
                  />
                  <span>{user.name}</span>
                </Space>
              </Button>
            </Dropdown>
          ) : (
            // User is not logged in
            <Space>
              <Button
                type="text"
                style={{
                  color: isHomePage ? 'white' : '#333',
                  fontWeight: '500',
                  border: isHomePage ? '1px solid rgba(255, 255, 255, 0.3)' : 'none',
                  background: isHomePage ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
                  borderRadius: '24px',
                  transition: 'all 0.3s ease'
                }}
                onClick={() => navigate('/register')}
              >
                Đăng ký
              </Button>
              <Button
                type="primary"
                style={{
                  background: isHomePage ? 'rgba(255, 255, 255, 0.2)' : '#00aa6c',
                  borderColor: isHomePage ? 'rgba(255, 255, 255, 0.3)' : '#00aa6c',
                  borderRadius: '24px',
                  padding: '0 24px',
                  height: '40px',
                  fontWeight: '600',
                  color: 'white',
                  transition: 'all 0.3s ease'
                }}
                onClick={() => navigate('/login')}
              >
                Đăng nhập
              </Button>
            </Space>
          )}
        </div>
      </div>
    </AntHeader>
  );
};

export default Header;
