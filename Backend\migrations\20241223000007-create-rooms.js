'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('rooms', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true
      },
      user_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      address_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'addresses',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      title: {
        type: Sequelize.STRING(255),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      price: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true
      },
      area: {
        type: Sequelize.FLOAT,
        allowNull: true
      },
      room_type: {
        type: Sequelize.ENUM('Phòng trọ', '<PERSON> cư', '<PERSON><PERSON><PERSON> riêng', '<PERSON><PERSON><PERSON><PERSON> sạn', 'Homestay'),
        defaultValue: 'Phòng trọ',
        allowNull: false
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false
      }
    });

    // Add indexes
    await queryInterface.addIndex('rooms', ['user_id']);
    await queryInterface.addIndex('rooms', ['address_id']);
    await queryInterface.addIndex('rooms', ['room_type']);
    await queryInterface.addIndex('rooms', ['price']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('rooms');
  }
};
