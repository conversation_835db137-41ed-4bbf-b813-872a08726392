const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
const { User } = require('../models');

const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// Email transporter setup
const createEmailTransporter = () => {
  return nodemailer.createTransport({
    service: 'gmail', // Use Gmail service
    host: 'smtp.gmail.com',
    port: 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    },
    tls: {
      rejectUnauthorized: false
    }
  });
};

const sendEmail = async (to, subject, html) => {
  try {
    console.log('📧 Attempting to send email to:', to);
    console.log('📧 SMTP Config:', {
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT,
      user: process.env.SMTP_USER,
      from: process.env.SMTP_FROM
    });

    const transporter = createEmailTransporter();
    const result = await transporter.sendMail({
      from: process.env.SMTP_FROM || process.env.SMTP_USER,
      to,
      subject,
      html
    });

    console.log('✅ Email sent successfully:', result.messageId);
    return true;
  } catch (error) {
    console.error('❌ Email sending error:', error.message);
    console.error('❌ Full error:', error);
    return false;
  }
};

const register = async (req, res, next) => {
  try {
    const { name, email, password, phone } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'Email already registered'
      });
    }

    // Generate email verification token
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');

    // Create new user
    const user = await User.create({
      name,
      email,
      password_hash: password, // Will be hashed by the model hook
      phone,
      email_verification_token: emailVerificationToken
    });

    // Send verification email
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/verify-email/${emailVerificationToken}`;
    const emailHtml = `
      <h2>Xác thực email của bạn</h2>
      <p>Chào ${name},</p>
      <p>Cảm ơn bạn đã đăng ký tài khoản. Vui lòng click vào link bên dưới để xác thực email:</p>
      <a href="${verificationUrl}" style="background-color: #00aa6c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Xác thực email</a>
      <p>Hoặc copy link này vào trình duyệt: ${verificationUrl}</p>
      <p>Link này sẽ hết hạn sau 24 giờ.</p>
    `;

    await sendEmail(email, 'Xác thực email - Du lịch Việt', emailHtml);

    // Generate token
    const token = generateToken(user.id);

    res.status(201).json({
      success: true,
      message: 'User registered successfully. Please check your email to verify your account.',
      data: {
        user,
        token
      }
    });
  } catch (error) {
    next(error);
  }
};

const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findOne({ 
      where: { email },
      attributes: ['id', 'name', 'email', 'password_hash', 'phone', 'role', 'created_at']
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Validate password
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Generate token
    const token = generateToken(user.id);

    // Remove password from response
    const userResponse = user.toJSON();

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userResponse,
        token
      }
    });
  } catch (error) {
    next(error);
  }
};

const getProfile = async (req, res, next) => {
  try {
    const user = req.user;
    res.json({
      success: true,
      data: { user }
    });
  } catch (error) {
    next(error);
  }
};

const updateProfile = async (req, res, next) => {
  try {
    const { name, phone } = req.body;
    const user = req.user;

    let updateData = {
      name: name || user.name,
      phone: phone || user.phone
    };

    // Handle avatar upload
    if (req.file) {
      const avatarUrl = `/uploads/${req.file.filename}`;
      updateData.avatar_url = avatarUrl;
      console.log('📸 Avatar uploaded:', avatarUrl);
    }

    await user.update(updateData);

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    next(error);
  }
};

const changePassword = async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const user = await User.findByPk(req.user.id, {
      attributes: ['id', 'password_hash']
    });

    // Validate current password
    const isValidPassword = await user.validatePassword(currentPassword);
    if (!isValidPassword) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Update password
    await user.update({ password_hash: newPassword });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Forgot Password
const forgotPassword = async (req, res, next) => {
  try {
    const { email } = req.body;

    const user = await User.findOne({ where: { email } });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found with this email'
      });
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetExpires = new Date(Date.now() + 3600000); // 1 hour

    await user.update({
      password_reset_token: resetToken,
      password_reset_expires: resetExpires
    });

    // Send reset email
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/reset-password/${resetToken}`;
    const emailHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Đặt lại mật khẩu - Du lịch Việt</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #00aa6c;">Đặt lại mật khẩu</h2>
          <p>Chào <strong>${user.name}</strong>,</p>
          <p>Bạn đã yêu cầu đặt lại mật khẩu cho tài khoản Du lịch Việt của mình.</p>
          <p>Click vào nút bên dưới để đặt lại mật khẩu:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background-color: #00aa6c; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">Đặt lại mật khẩu</a>
          </div>
          <p>Hoặc copy link này vào trình duyệt:</p>
          <p style="word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 3px;">${resetUrl}</p>
          <p><strong>Lưu ý:</strong> Link này sẽ hết hạn sau 1 giờ.</p>
          <p>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.</p>
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          <p style="font-size: 12px; color: #666;">
            Email này được gửi từ hệ thống Du lịch Việt.<br>
            Vui lòng không trả lời email này.
          </p>
        </div>
      </body>
      </html>
    `;

    const emailSent = await sendEmail(email, 'Đặt lại mật khẩu - Du lịch Việt', emailHtml);

    if (!emailSent) {
      return res.status(500).json({
        success: false,
        message: 'Failed to send reset email'
      });
    }

    res.json({
      success: true,
      message: 'Password reset email sent successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Reset Password
const resetPassword = async (req, res, next) => {
  try {
    const { token, newPassword } = req.body;

    const user = await User.findOne({
      where: {
        password_reset_token: token,
        password_reset_expires: {
          [require('sequelize').Op.gt]: new Date()
        }
      }
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token'
      });
    }

    // Update password and clear reset token
    await user.update({
      password_hash: newPassword,
      password_reset_token: null,
      password_reset_expires: null
    });

    res.json({
      success: true,
      message: 'Password reset successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Verify Email
const verifyEmail = async (req, res, next) => {
  try {
    const { token } = req.params;
    console.log('🔍 Email verification attempt with token:', token);

    // Debug: Check all users with verification tokens
    const allUsersWithTokens = await User.findAll({
      where: {
        email_verification_token: { [require('sequelize').Op.ne]: null }
      },
      attributes: ['email', 'email_verification_token', 'email_verified']
    });
    console.log('📋 All users with verification tokens:', allUsersWithTokens.map(u => ({
      email: u.email,
      token: u.email_verification_token,
      verified: u.email_verified
    })));

    const user = await User.findOne({
      where: { email_verification_token: token }
    });

    console.log('👤 User found:', user ? `${user.email} (verified: ${user.email_verified})` : 'null');

    if (!user) {
      console.log('❌ Invalid verification token');
      return res.status(400).json({
        success: false,
        message: 'Invalid verification token'
      });
    }

    if (user.email_verified) {
      console.log('⚠️ Email already verified');
      return res.status(400).json({
        success: false,
        message: 'Email already verified'
      });
    }

    await user.update({
      email_verified: true,
      email_verification_token: null
    });

    console.log('✅ Email verified successfully for:', user.email);

    res.json({
      success: true,
      message: 'Email verified successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Resend Verification Email
const resendVerification = async (req, res, next) => {
  try {
    const { email } = req.body;
    console.log('📧 Resend verification request for email:', email);

    const user = await User.findOne({ where: { email } });
    if (!user) {
      console.log('❌ User not found:', email);
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.email_verified) {
      console.log('⚠️ Email already verified:', email);
      return res.status(400).json({
        success: false,
        message: 'Email already verified'
      });
    }

    // Generate new verification token
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');
    console.log('🔑 Generated new token for:', email, 'Token:', emailVerificationToken);
    await user.update({ email_verification_token: emailVerificationToken });

    // Send verification email
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/verify-email/${emailVerificationToken}`;
    const emailHtml = `
      <h2>Xác thực email của bạn</h2>
      <p>Chào ${user.name},</p>
      <p>Vui lòng click vào link bên dưới để xác thực email:</p>
      <a href="${verificationUrl}" style="background-color: #00aa6c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Xác thực email</a>
      <p>Hoặc copy link này vào trình duyệt: ${verificationUrl}</p>
    `;

    const emailSent = await sendEmail(email, 'Xác thực email - Du lịch Việt', emailHtml);

    if (!emailSent) {
      return res.status(500).json({
        success: false,
        message: 'Failed to send verification email'
      });
    }

    res.json({
      success: true,
      message: 'Verification email sent successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerification
};
