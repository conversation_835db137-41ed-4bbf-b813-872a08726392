'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableInfo = await queryInterface.describeTable('users');
    
    // Add google_id field
    if (!tableInfo.google_id) {
      await queryInterface.addColumn('users', 'google_id', {
        type: Sequelize.STRING(100),
        allowNull: true,
        unique: true
      });
    }

    // Add avatar_url field
    if (!tableInfo.avatar_url) {
      await queryInterface.addColumn('users', 'avatar_url', {
        type: Sequelize.TEXT,
        allowNull: true
      });
    }

    // Add email_verified field
    if (!tableInfo.email_verified) {
      await queryInterface.addColumn('users', 'email_verified', {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false
      });
    }

    // Add email_verification_token field
    if (!tableInfo.email_verification_token) {
      await queryInterface.addColumn('users', 'email_verification_token', {
        type: Sequelize.STRING(255),
        allowNull: true
      });
    }

    // Add password_reset_token field
    if (!tableInfo.password_reset_token) {
      await queryInterface.addColumn('users', 'password_reset_token', {
        type: Sequelize.STRING(255),
        allowNull: true
      });
    }

    // Add password_reset_expires field
    if (!tableInfo.password_reset_expires) {
      await queryInterface.addColumn('users', 'password_reset_expires', {
        type: Sequelize.DATE,
        allowNull: true
      });
    }

    // Add indexes
    await queryInterface.addIndex('users', ['google_id']);
    await queryInterface.addIndex('users', ['email_verification_token']);
    await queryInterface.addIndex('users', ['password_reset_token']);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes
    await queryInterface.removeIndex('users', ['google_id']);
    await queryInterface.removeIndex('users', ['email_verification_token']);
    await queryInterface.removeIndex('users', ['password_reset_token']);

    // Remove columns
    await queryInterface.removeColumn('users', 'google_id');
    await queryInterface.removeColumn('users', 'avatar_url');
    await queryInterface.removeColumn('users', 'email_verified');
    await queryInterface.removeColumn('users', 'email_verification_token');
    await queryInterface.removeColumn('users', 'password_reset_token');
    await queryInterface.removeColumn('users', 'password_reset_expires');
  }
};
