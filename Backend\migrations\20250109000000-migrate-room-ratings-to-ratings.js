'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Check if room_ratings table exists and has data
    try {
      const [roomRatings] = await queryInterface.sequelize.query(
        "SELECT * FROM room_ratings",
        { type: Sequelize.QueryTypes.SELECT }
      );

      console.log(`Found ${roomRatings.length} room ratings to migrate`);

      // Migrate each room rating to the ratings table
      for (const roomRating of roomRatings) {
        try {
          await queryInterface.sequelize.query(
            `INSERT INTO ratings (user_id, target_type, target_id, rating, comment, created_at) 
             VALUES (?, 'room', ?, ?, ?, ?)`,
            {
              replacements: [
                roomRating.user_id,
                roomRating.room_id,
                roomRating.rating,
                roomRating.comment,
                roomRating.created_at
              ],
              type: Sequelize.QueryTypes.INSERT
            }
          );
          console.log(`Migrated room rating ${roomRating.id}`);
        } catch (error) {
          console.log(`Error migrating room rating ${roomRating.id}:`, error.message);
          // Continue with next rating if this one fails (might be duplicate)
        }
      }

      console.log('Room ratings migration completed');
    } catch (error) {
      console.log('Room_ratings table does not exist or is empty:', error.message);
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Remove room ratings from ratings table
    await queryInterface.sequelize.query(
      "DELETE FROM ratings WHERE target_type = 'room'",
      { type: Sequelize.QueryTypes.DELETE }
    );
    console.log('Removed room ratings from ratings table');
  }
};
