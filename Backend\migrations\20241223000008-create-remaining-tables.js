'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create reviews table
    await queryInterface.createTable('reviews', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true
      },
      user_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      place_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'places',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      title: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      rating: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
          max: 5
        }
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false
      }
    });

    // Create room_images table
    await queryInterface.createTable('room_images', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true
      },
      room_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'rooms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      image_url: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      caption: {
        type: Sequelize.STRING(255),
        allowNull: true
      }
    });

    // Create ratings table
    await queryInterface.createTable('ratings', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true
      },
      user_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      target_type: {
        type: Sequelize.ENUM('room', 'review'),
        allowNull: false
      },
      target_id: {
        type: Sequelize.BIGINT,
        allowNull: false
      },
      rating: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
          max: 5
        }
      },
      comment: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false
      }
    });

    // Create favorites table
    await queryInterface.createTable('favorites', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true
      },
      user_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      room_id: {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'rooms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      created_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW,
        allowNull: false
      }
    });

    // Add indexes
    await queryInterface.addIndex('reviews', ['user_id']);
    await queryInterface.addIndex('reviews', ['place_id']);
    await queryInterface.addIndex('reviews', ['rating']);

    await queryInterface.addIndex('room_images', ['room_id']);

    await queryInterface.addIndex('ratings', ['user_id']);
    await queryInterface.addIndex('ratings', ['target_type', 'target_id']);
    await queryInterface.addConstraint('ratings', {
      fields: ['user_id', 'target_type', 'target_id'],
      type: 'unique',
      name: 'unique_user_rating'
    });

    await queryInterface.addIndex('favorites', ['user_id']);
    await queryInterface.addIndex('favorites', ['room_id']);
    await queryInterface.addConstraint('favorites', {
      fields: ['user_id', 'room_id'],
      type: 'unique',
      name: 'unique_user_room_favorite'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('favorites');
    await queryInterface.dropTable('ratings');
    await queryInterface.dropTable('room_images');
    await queryInterface.dropTable('reviews');
  }
};
