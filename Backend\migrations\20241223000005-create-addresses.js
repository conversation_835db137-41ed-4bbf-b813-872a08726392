'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('addresses', {
      id: {
        type: Sequelize.BIGINT,
        primaryKey: true,
        autoIncrement: true
      },
      ward_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'wards',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      street_detail: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      lat: {
        type: Sequelize.DECIMAL(10, 6),
        allowNull: true
      },
      lng: {
        type: Sequelize.DECIMAL(10, 6),
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('addresses', ['ward_id']);
    await queryInterface.addIndex('addresses', ['lat', 'lng']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('addresses');
  }
};
