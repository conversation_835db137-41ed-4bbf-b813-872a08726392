import React, { useState, useRef, useEffect } from 'react';
import { Button, Space, Upload, message, Tooltip } from 'antd';
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,
  LinkOutlined,
  PictureOutlined,
  UndoOutlined,
  RedoOutlined
} from '@ant-design/icons';
import { getApiUrl } from '../../services/api';

const HtmlEditor = ({ value = '', onChange, placeholder = 'Nhập nội dung...' }) => {
  const editorRef = useRef(null);
  const [uploading, setUploading] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const execCommand = (command, value = null) => {
    document.execCommand(command, false, value);
    editorRef.current.focus();
    handleContentChange();
  };

  const handleContentChange = () => {
    if (editorRef.current && onChange && !isUpdating) {
      const newContent = editorRef.current.innerHTML;
      onChange(newContent);
    }
  };

  // Update editor content when value prop changes (but avoid infinite loop)
  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.innerHTML) {
      setIsUpdating(true);
      editorRef.current.innerHTML = value;
      setIsUpdating(false);
    }
  }, [value]);

  const handleImageUpload = async (file) => {
    setUploading(true);
    const formData = new FormData();
    formData.append('images', file);

    try {
      const response = await fetch(`${getApiUrl()}/upload/images`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token') || ''}`
        },
        body: formData
      });

      const result = await response.json();
      if (result.success) {
        const imageUrl = result.data.files[0].url;
        const imgHtml = `<img src="${imageUrl}" alt="Uploaded image" style="max-width: 100%; height: auto; margin: 10px 0;" />`;
        
        // Insert image at cursor position
        if (editorRef.current) {
          editorRef.current.focus();
          document.execCommand('insertHTML', false, imgHtml);
          handleContentChange();
        }
        
        message.success('Upload ảnh thành công');
      } else {
        message.error('Upload ảnh thất bại');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      message.error('Upload ảnh thất bại');
    } finally {
      setUploading(false);
    }
    
    return false; // Prevent default upload behavior
  };

  const insertLink = () => {
    const url = prompt('Nhập URL:');
    if (url) {
      execCommand('createLink', url);
    }
  };

  const toolbarButtons = [
    { command: 'bold', icon: <BoldOutlined />, tooltip: 'In đậm' },
    { command: 'italic', icon: <ItalicOutlined />, tooltip: 'In nghiêng' },
    { command: 'underline', icon: <UnderlineOutlined />, tooltip: 'Gạch chân' },
    { command: 'insertOrderedList', icon: <OrderedListOutlined />, tooltip: 'Danh sách có số' },
    { command: 'insertUnorderedList', icon: <UnorderedListOutlined />, tooltip: 'Danh sách không số' },
    { command: 'undo', icon: <UndoOutlined />, tooltip: 'Hoàn tác' },
    { command: 'redo', icon: <RedoOutlined />, tooltip: 'Làm lại' }
  ];

  return (
    <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', position: 'relative' }}>
      {/* Toolbar */}
      <div style={{ 
        padding: '8px 12px', 
        borderBottom: '1px solid #d9d9d9', 
        backgroundColor: '#fafafa',
        borderRadius: '6px 6px 0 0'
      }}>
        <Space wrap>
          {toolbarButtons.map((btn, index) => (
            <Tooltip key={index} title={btn.tooltip}>
              <Button
                size="small"
                icon={btn.icon}
                onClick={() => execCommand(btn.command)}
                style={{ border: 'none', boxShadow: 'none' }}
              />
            </Tooltip>
          ))}
          
          <Tooltip title="Chèn link">
            <Button
              size="small"
              icon={<LinkOutlined />}
              onClick={insertLink}
              style={{ border: 'none', boxShadow: 'none' }}
            />
          </Tooltip>

          <Upload
            beforeUpload={handleImageUpload}
            showUploadList={false}
            accept="image/*"
          >
            <Tooltip title="Chèn ảnh">
              <Button
                size="small"
                icon={<PictureOutlined />}
                loading={uploading}
                style={{ border: 'none', boxShadow: 'none' }}
              />
            </Tooltip>
          </Upload>

          {/* Format buttons */}
          <Tooltip title="Tiêu đề 1">
            <Button
              size="small"
              onClick={() => execCommand('formatBlock', 'h1')}
              style={{ border: 'none', boxShadow: 'none', fontSize: '12px' }}
            >
              H1
            </Button>
          </Tooltip>
          
          <Tooltip title="Tiêu đề 2">
            <Button
              size="small"
              onClick={() => execCommand('formatBlock', 'h2')}
              style={{ border: 'none', boxShadow: 'none', fontSize: '12px' }}
            >
              H2
            </Button>
          </Tooltip>
          
          <Tooltip title="Tiêu đề 3">
            <Button
              size="small"
              onClick={() => execCommand('formatBlock', 'h3')}
              style={{ border: 'none', boxShadow: 'none', fontSize: '12px' }}
            >
              H3
            </Button>
          </Tooltip>
        </Space>
      </div>

      {/* Editor */}
      <div
        ref={editorRef}
        contentEditable
        suppressContentEditableWarning
        onInput={handleContentChange}
        onBlur={handleContentChange}
        style={{
          minHeight: '200px',
          padding: '12px',
          outline: 'none',
          lineHeight: '1.6',
          fontSize: '14px',
          backgroundColor: '#fff'
        }}
        onFocus={(e) => {
          if (e.target.innerHTML === '' || e.target.innerHTML === '<br>') {
            e.target.innerHTML = '';
          }
        }}
        onKeyDown={(e) => {
          // Handle placeholder
          if (e.target.innerHTML === '' && e.key !== 'Backspace' && e.key !== 'Delete') {
            e.target.innerHTML = '';
          }
        }}
      />

      {/* Placeholder when empty */}
      {(!value || value === '' || value === '<br>') && (
        <div style={{
          position: 'absolute',
          top: '60px',
          left: '12px',
          color: '#bfbfbf',
          pointerEvents: 'none',
          fontSize: '14px'
        }}>
          {placeholder}
        </div>
      )}
      
      {/* Help text */}
      <div style={{ 
        padding: '8px 12px', 
        borderTop: '1px solid #f0f0f0', 
        fontSize: '12px', 
        color: '#666',
        backgroundColor: '#fafafa',
        borderRadius: '0 0 6px 6px'
      }}>
        💡 Mẹo: Sử dụng các nút trên thanh công cụ để định dạng text và thêm ảnh vào nội dung
      </div>
    </div>
  );
};

export default HtmlEditor;
