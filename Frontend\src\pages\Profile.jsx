import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Upload,
  Avatar,
  Row,
  Col,
  Typography,
  Divider,
  Tabs,
  Statistic,
  List,
  Tag,
  Modal,
  Alert,
  Popconfirm
} from 'antd';
import { useMessage } from '../components/CustomMessage';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  CameraOutlined,
  EditOutlined,
  SaveOutlined,
  HomeOutlined,
  StarOutlined,
  LockOutlined,
  SendOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/layout/Layout';
import { authAPI, roomAPI, reviewAPI } from '../services/api';
import { formatDateTime } from '../utils/formatters';
import { getServerBaseUrl } from '../utils/apiConfig';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const Profile = () => {
  const { user, updateUser } = useAuth();
  const navigate = useNavigate();
  const message = useMessage();

  // Function to refresh user data from server
  const refreshUserData = useCallback(async () => {
    try {
      const response = await authAPI.getProfile();
      if (response.success) {
        updateUser(response.data.user);
        console.log('🔄 User data refreshed from server');
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  }, [updateUser]);
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [editing, setEditing] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [userStats, setUserStats] = useState({
    totalRooms: 0,
    totalReviews: 0
  });
  const [userRooms, setUserRooms] = useState([]);
  const [userReviews, setUserReviews] = useState([]);

  // Define all callback functions first
  const fetchUserStats = useCallback(async () => {
    try {
      // Fetch real data from APIs
      const [roomsResponse, reviewsResponse] = await Promise.all([
        roomAPI.getUserRooms({ limit: 1000 }), // Get all to count
        reviewAPI.getUserReviews({ limit: 1000 }) // Get all to count
      ]);

      let totalRooms = 0;
      let totalReviews = 0;

      if (roomsResponse.success) {
        totalRooms = roomsResponse.data.pagination?.total_items || roomsResponse.data.rooms?.length || 0;
      }

      if (reviewsResponse.success) {
        totalReviews = reviewsResponse.data.pagination?.total_items || reviewsResponse.data.reviews?.length || 0;
      }

      setUserStats({
        totalRooms,
        totalReviews
      });
    } catch (error) {
      console.error('Error fetching user stats:', error);
      // Set default values on error
      setUserStats({
        totalRooms: 0,
        totalReviews: 0
      });
    }
  }, []);

  const fetchUserRooms = useCallback(async () => {
    try {
      const response = await roomAPI.getUserRooms({ limit: 5 }); // Get latest 5
      console.log('📊 User rooms response:', response);

      if (response.success) {
        setUserRooms(response.data.rooms || []);
      }
    } catch (error) {
      console.error('Error fetching user rooms:', error);
      setUserRooms([]);
    }
  }, []);

  const fetchUserReviews = useCallback(async () => {
    try {
      const response = await reviewAPI.getUserReviews({ limit: 5 }); // Get latest 5
      console.log('📝 User reviews response:', response);

      if (response.success) {
        setUserReviews(response.data.reviews || []);
      }
    } catch (error) {
      console.error('Error fetching user reviews:', error);
      setUserReviews([]);
    }
  }, []);

  const handleUpdateProfile = async (values) => {
    setLoading(true);
    try {
      const response = await authAPI.updateProfile(values);
      console.log('📝 Update profile response:', response);

      if (response.success) {
        updateUser(response.data.user || response.data);
        message.success('Cập nhật thông tin thành công!');
        setEditing(false);
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      message.error(error.message || 'Không thể cập nhật thông tin');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarUpload = async (file) => {
    // Validate file type
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('Chỉ có thể upload file ảnh!');
      return false;
    }

    // Validate file size (max 5MB)
    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error('Ảnh phải nhỏ hơn 5MB!');
      return false;
    }

    const formData = new FormData();
    formData.append('avatar', file);

    try {
      setLoading(true);
      console.log('📤 Uploading avatar...');
      const response = await authAPI.updateProfile(formData);
      console.log('📤 Avatar upload response:', response);

      if (response.success) {
        console.log('✅ Avatar upload successful');
        console.log('🖼️ New avatar URL:', response.data.user?.avatar_url || response.data?.avatar_url);

        // Cập nhật user context với avatar mới
        const newAvatarUrl = response.data.user?.avatar_url || response.data?.avatar_url;
        const updatedUser = { ...user, avatar_url: newAvatarUrl };

        console.log('🔄 Updating user context:', updatedUser);
        updateUser(updatedUser);

        // Force refresh user data from server
        setTimeout(() => {
          refreshUserData();
        }, 500);

        message.success('Cập nhật avatar thành công!');
      } else {
        console.error('❌ Avatar upload failed:', response);
        message.error(response.message || 'Không thể cập nhật avatar');
      }
    } catch (error) {
      console.error('❌ Avatar upload error:', error);
      message.error('Không thể cập nhật avatar');
    } finally {
      setLoading(false);
    }

    return false; // Prevent default upload
  };

  // Handle change password
  const handleChangePassword = async (values) => {
    console.log('🔐 Changing password...');
    setPasswordLoading(true);
    try {
      const response = await authAPI.changePassword({
        currentPassword: values.currentPassword,
        newPassword: values.newPassword
      });

      console.log('✅ Change password response:', response);
      if (response.success) {
        message.success('Đổi mật khẩu thành công!');
        passwordForm.resetFields();
        setPasswordModalVisible(false);
      }
    } catch (error) {
      console.error('❌ Error changing password:', error);
      message.error(error.message || 'Không thể đổi mật khẩu');
    } finally {
      setPasswordLoading(false);
    }
  };

  // Handle resend verification email
  const handleResendVerification = async () => {
    setResendLoading(true);
    try {
      const response = await authAPI.resendVerification(user.email);
     
      if (response.success) {
        console.log("✅ Resend verification successful");

        // Hiển thị thông báo
        message.success('Email xác thực đã được gửi! Vui lòng kiểm tra hộp thư.');

        // Refresh user data để cập nhật token mới
        setTimeout(() => {
          refreshUserData();
        }, 1000);
      } else {
        message.error(response.message || 'Có lỗi xảy ra khi gửi email xác thực');
      }
    } catch (error) {
      console.error('Error resending verification:', error);
      message.error(error.message || 'Không thể gửi email xác thực');
    } finally {
      setResendLoading(false);
    }
  };

  // Handle delete room
  const handleDeleteRoom = async (roomId) => {
    try {
      const response = await roomAPI.deleteRoom(roomId);

      if (response.success) {
        message.success('Xóa phòng thành công!');
        // Refresh data
        fetchUserRooms();
        fetchUserStats();
      }
    } catch (error) {
      console.error('Error deleting room:', error);
      message.error(error.message || 'Không thể xóa phòng');
    }
  };

  // Handle delete review
  const handleDeleteReview = async (reviewId) => {
    try {
      const response = await reviewAPI.deleteReview(reviewId);

      if (response.success) {
        message.success('Xóa bài viết thành công!');
        // Refresh data
        fetchUserReviews();
        fetchUserStats();
      }
    } catch (error) {
      console.error('Error deleting review:', error);
      message.error(error.message || 'Không thể xóa bài viết');
    }
  };

  // Effect để set form values khi user thay đổi
  useEffect(() => {
    if (user) {
      form.setFieldsValue({
        name: user.name,
        email: user.email,
        phone: user.phone
      });
    }
  }, [user, form]);

  // Effect để load data khi component mount
  useEffect(() => {
    console.log("Profile component mounted");

    // Chỉ refresh user data nếu chưa có user hoặc cần update
    if (!user) {
      refreshUserData();
    }

    // Load stats và data
    if (user) {
      fetchUserStats();
      fetchUserRooms();
      fetchUserReviews();
    }
  }, [user, refreshUserData, fetchUserStats, fetchUserRooms, fetchUserReviews]); // Dependencies được memoized

  return (
    <Layout>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '24px' }}>
        <Row gutter={[24, 24]}>
          {/* Profile Info */}
          <Col xs={24} lg={8}>
            <Card>
              <div style={{ textAlign: 'center', marginBottom: '24px' }}>
                <Upload
                  showUploadList={false}
                  beforeUpload={handleAvatarUpload}
                  accept="image/*"
                >
                  <div style={{ position: 'relative', display: 'inline-block' }}>
                    <Avatar
                      size={120}
                      src={user?.avatar_url ? (
                        user.avatar_url.startsWith('http')
                          ? `${user.avatar_url}?t=${Date.now()}`
                          : `${getServerBaseUrl()}${user.avatar_url}?t=${Date.now()}`
                      ) : null}
                      icon={<UserOutlined />}
                      style={{ cursor: 'pointer' }}
                    />
                    <div
                      style={{
                        position: 'absolute',
                        bottom: 0,
                        right: 0,
                        backgroundColor: '#1890ff',
                        borderRadius: '50%',
                        width: '32px',
                        height: '32px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        cursor: 'pointer'
                      }}
                    >
                      <CameraOutlined style={{ color: 'white', fontSize: '14px' }} />
                    </div>
                  </div>
                </Upload>
                
                <Title level={4} style={{ marginTop: '16px', marginBottom: '8px' }}>
                  {user?.name}
                </Title>
                <Text type="secondary">{user?.email}</Text>

                <div style={{ marginTop: '8px' }}>
                  {user?.email_verified ? (
                    <Tag color="green" icon={<CheckCircleOutlined />}>
                      Email đã xác thực
                    </Tag>
                  ) : (
                    <div>
                      <Tag color="orange" icon={<ExclamationCircleOutlined />}>
                        Email chưa xác thực
                      </Tag>
                      <Button
                        type="link"
                        size="small"
                        icon={<SendOutlined />}
                        loading={resendLoading}
                        onClick={handleResendVerification}
                        style={{ padding: '0 4px', height: 'auto' }}
                      >
                        Gửi lại email xác thực
                      </Button>
                    </div>
                  )}
                </div>

                <Divider />

                {/* Action Buttons */}
                <div style={{ textAlign: 'center' }}>

                  <Button
                    type="primary"
                    icon={<LockOutlined />}
                    onClick={() => setPasswordModalVisible(true)}
                    style={{ marginBottom: '8px', width: '100%' }}
                  >
                    Đổi mật khẩu
                  </Button>
                </div>
              </div>

              {/* Stats */}
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="Phòng"
                    value={userStats.totalRooms}
                    prefix={<HomeOutlined />}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="Reviews"
                    value={userStats.totalReviews}
                    prefix={<StarOutlined />}
                  />
                </Col>

              </Row>
            </Card>
          </Col>

          {/* Profile Details & Activities */}
          <Col xs={24} lg={16}>
            <Card>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
                <Title level={4} style={{ margin: 0 }}>
                  Thông tin cá nhân
                </Title>
                <Button
                  type={editing ? 'primary' : 'default'}
                  icon={editing ? <SaveOutlined /> : <EditOutlined />}
                  onClick={() => {
                    if (editing) {
                      form.submit();
                    } else {
                      setEditing(true);
                    }
                  }}
                  loading={loading}
                >
                  {editing ? 'Lưu' : 'Chỉnh sửa'}
                </Button>
              </div>

              <Form
                form={form}
                layout="vertical"
                onFinish={handleUpdateProfile}
              >
                <Row gutter={16}>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="name"
                      label="Họ và tên"
                      rules={[{ required: true, message: 'Vui lòng nhập họ tên!' }]}
                    >
                      <Input
                        prefix={<UserOutlined />}
                        disabled={!editing}
                        placeholder="Nhập họ và tên"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="email"
                      label="Email"
                      rules={[
                        { required: true, message: 'Vui lòng nhập email!' },
                        { type: 'email', message: 'Email không hợp lệ!' }
                      ]}
                    >
                      <Input
                        prefix={<MailOutlined />}
                        disabled={!editing}
                        placeholder="Nhập email"
                      />
                    </Form.Item>
                  </Col>
                  <Col xs={24} sm={12}>
                    <Form.Item
                      name="phone"
                      label="Số điện thoại"
                    >
                      <Input
                        prefix={<PhoneOutlined />}
                        disabled={!editing}
                        placeholder="Nhập số điện thoại"
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>

              <Divider />

              <Tabs defaultActiveKey="rooms">
                <TabPane tab="Phòng của tôi" key="rooms">
                  <div style={{ marginBottom: '16px', textAlign: 'right' }}>
                    <Button
                      type="primary"
                      onClick={() => navigate('/my-rooms')}
                    >
                      Xem tất cả phòng
                    </Button>
                  </div>
                  <List
                    dataSource={userRooms}
                    renderItem={(room) => (
                      <List.Item
                        actions={[
                          <Button
                            type="link"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => navigate(`/rooms/${room.id}`)}
                          >
                            Xem
                          </Button>,
                          // Chỉ hiển thị nút sửa cho phòng chưa duyệt
                          ...(room.status === 'pending' ? [
                            <Button
                              type="link"
                              size="small"
                              icon={<EditOutlined />}
                              onClick={() => navigate(`/edit-room/${room.id}`)}
                            >
                              Sửa
                            </Button>
                          ] : []),
                          <Popconfirm
                            title="Xóa phòng"
                            description="Bạn có chắc chắn muốn xóa phòng này?"
                            onConfirm={() => handleDeleteRoom(room.id)}
                            okText="Xóa"
                            cancelText="Hủy"
                          >
                            <Button
                              type="link"
                              size="small"
                              danger
                              icon={<DeleteOutlined />}
                            >
                              Xóa
                            </Button>
                          </Popconfirm>
                        ]}
                      >
                        <List.Item.Meta
                          title={
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <span>{room.title}</span>
                              <Tag color={room.status === 'active' ? 'green' : room.status === 'pending' ? 'orange' : 'red'}>
                                {room.status === 'active' ? 'Đã duyệt' :
                                 room.status === 'pending' ? 'Chờ duyệt' : 'Từ chối'}
                              </Tag>
                            </div>
                          }
                          description={
                            <div>
                              <div>{room.price?.toLocaleString()}đ/tháng • {room.area}m²</div>
                              <Text type="secondary">{formatDateTime(room.created_at)}</Text>
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                    locale={{ emptyText: 'Chưa có phòng nào' }}
                  />
                </TabPane>
                
                <TabPane tab="Reviews của tôi" key="reviews">
                  <div style={{ marginBottom: '16px', textAlign: 'right' }}>
                    <Button
                      type="primary"
                      onClick={() => navigate('/my-reviews')}
                    >
                      Xem tất cả bài viết
                    </Button>
                  </div>
                  <List
                    dataSource={userReviews}
                    renderItem={(review) => (
                      <List.Item
                        actions={[
                          <Button
                            type="link"
                            size="small"
                            icon={<EyeOutlined />}
                            onClick={() => navigate(`/reviews/${review.id}`)}
                          >
                            Xem
                          </Button>,
                          // Chỉ hiển thị nút sửa cho reviews chưa duyệt
                          ...(review.status === 'pending' ? [
                            <Button
                              type="link"
                              size="small"
                              icon={<EditOutlined />}
                              onClick={() => navigate(`/edit-review/${review.id}`)}
                            >
                              Sửa
                            </Button>
                          ] : []),
                          <Popconfirm
                            title="Xóa bài viết"
                            description="Bạn có chắc chắn muốn xóa bài viết này?"
                            onConfirm={() => handleDeleteReview(review.id)}
                            okText="Xóa"
                            cancelText="Hủy"
                          >
                            <Button
                              type="link"
                              size="small"
                              danger
                              icon={<DeleteOutlined />}
                            >
                              Xóa
                            </Button>
                          </Popconfirm>
                        ]}
                      >
                        <List.Item.Meta
                          title={
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <span>{review.title}</span>
                              <Tag color={review.status === 'approved' ? 'green' : review.status === 'pending' ? 'orange' : 'red'}>
                                {review.status === 'approved' ? 'Đã duyệt' :
                                 review.status === 'pending' ? 'Chờ duyệt' : 'Từ chối'}
                              </Tag>
                            </div>
                          }
                          description={
                            <div>
                              <div style={{ marginBottom: '4px' }}>
                                {[...Array(review.rating || 0)].map((_, i) => (
                                  <StarOutlined key={i} style={{ color: '#faad14' }} />
                                ))}
                                <span style={{ marginLeft: '8px' }}>
                                  {review.category && (
                                    <Tag size="small">{review.category}</Tag>
                                  )}
                                </span>
                              </div>
                              <Text type="secondary">
                                {review.location_name} • {formatDateTime(review.created_at)}
                              </Text>
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                    locale={{ emptyText: 'Chưa có review nào' }}
                  />
                </TabPane>
              </Tabs>
            </Card>
          </Col>
        </Row>

        {/* Change Password Modal */}
        <Modal
          title="Đổi mật khẩu"
          open={passwordModalVisible}
          onCancel={() => {
            setPasswordModalVisible(false);
            passwordForm.resetFields();
          }}
          footer={null}
          width={400}
        >
          <Form
            form={passwordForm}
            layout="vertical"
            onFinish={handleChangePassword}
          >
            <Form.Item
              label="Mật khẩu hiện tại"
              name="currentPassword"
              rules={[
                { required: true, message: 'Vui lòng nhập mật khẩu hiện tại!' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Nhập mật khẩu hiện tại"
                size="large"
              />
            </Form.Item>

            <Form.Item
              label="Mật khẩu mới"
              name="newPassword"
              rules={[
                { required: true, message: 'Vui lòng nhập mật khẩu mới!' },
                { min: 6, message: 'Mật khẩu phải có ít nhất 6 ký tự!' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Nhập mật khẩu mới"
                size="large"
              />
            </Form.Item>

            <Form.Item
              label="Xác nhận mật khẩu mới"
              name="confirmPassword"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: 'Vui lòng xác nhận mật khẩu mới!' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('Mật khẩu xác nhận không khớp!'));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined />}
                placeholder="Xác nhận mật khẩu mới"
                size="large"
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
              <Button
                onClick={() => {
                  setPasswordModalVisible(false);
                  passwordForm.resetFields();
                }}
                style={{ marginRight: '8px' }}
              >
                Hủy
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={passwordLoading}
                icon={<SaveOutlined />}
              >
                Đổi mật khẩu
              </Button>
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </Layout>
  );
};

export default Profile;
