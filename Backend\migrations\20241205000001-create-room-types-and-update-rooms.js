'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Check if room_types table exists
    const tableExists = await queryInterface.sequelize.query(
      "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'room_types'"
    );

    if (tableExists[0][0].count === 0) {
      // Create room_types table
      await queryInterface.createTable('room_types', {
        id: {
          type: Sequelize.BIGINT,
          primaryKey: true,
          autoIncrement: true
        },
        name: {
          type: Sequelize.STRING(100),
          allowNull: false,
          unique: true
        },
        description: {
          type: Sequelize.TEXT,
          allowNull: true
        },
        is_active: {
          type: Sequelize.BOOLEAN,
          defaultValue: true,
          allowNull: false
        },
        created_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW
        },
        updated_at: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.NOW
        }
      });

      // Insert default room types
      await queryInterface.bulkInsert('room_types', [
        {
          name: '<PERSON>òng trọ',
          description: '<PERSON>òng trọ sinh viên, người đi làm',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Chung cư',
          description: 'Căn hộ chung cư',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Nhà riêng',
          description: 'Nhà nguyên căn',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Khách sạn',
          description: 'Phòng khách sạn',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        },
        {
          name: 'Homestay',
          description: 'Homestay du lịch',
          is_active: true,
          created_at: new Date(),
          updated_at: new Date()
        }
      ]);
    }

    // Check if room_type_id column exists
    const columnExists = await queryInterface.sequelize.query(
      "SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'rooms' AND column_name = 'room_type_id'"
    );

    if (columnExists[0][0].count === 0) {
      // Add room_type_id column to rooms table
      await queryInterface.addColumn('rooms', 'room_type_id', {
        type: Sequelize.BIGINT,
        allowNull: true, // Temporarily allow null for migration
        references: {
          model: 'room_types',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      });

      // Update existing rooms to use room_type_id
      const [roomTypes] = await queryInterface.sequelize.query(
        'SELECT id, name FROM room_types'
      );

      for (const roomType of roomTypes) {
        await queryInterface.sequelize.query(
          `UPDATE rooms SET room_type_id = ${roomType.id} WHERE room_type = '${roomType.name}'`
        );
      }

      // Make room_type_id NOT NULL after data migration
      await queryInterface.changeColumn('rooms', 'room_type_id', {
        type: Sequelize.BIGINT,
        allowNull: false,
        references: {
          model: 'room_types',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'RESTRICT'
      });

      // Check if room_type column exists before removing
      const oldColumnExists = await queryInterface.sequelize.query(
        "SELECT COUNT(*) as count FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'rooms' AND column_name = 'room_type'"
      );

      if (oldColumnExists[0][0].count > 0) {
        // Remove old room_type column
        await queryInterface.removeColumn('rooms', 'room_type');
      }

      // Add index for room_type_id
      await queryInterface.addIndex('rooms', ['room_type_id']);
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Add back room_type column
    await queryInterface.addColumn('rooms', 'room_type', {
      type: Sequelize.ENUM('Phòng trọ', 'Chung cư', 'Nhà riêng', 'Khách sạn', 'Homestay'),
      defaultValue: 'Phòng trọ',
      allowNull: false
    });

    // Migrate data back
    const [roomTypes] = await queryInterface.sequelize.query(
      'SELECT id, name FROM room_types'
    );

    for (const roomType of roomTypes) {
      await queryInterface.sequelize.query(
        `UPDATE rooms SET room_type = '${roomType.name}' WHERE room_type_id = ${roomType.id}`
      );
    }

    // Remove room_type_id column
    await queryInterface.removeColumn('rooms', 'room_type_id');

    // Drop room_types table
    await queryInterface.dropTable('room_types');

    // Add back index for room_type
    await queryInterface.addIndex('rooms', ['room_type']);
  }
};
