'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add status column to rooms table
    const roomsTableInfo = await queryInterface.describeTable('rooms');
    if (!roomsTableInfo.status) {
      await queryInterface.addColumn('rooms', 'status', {
        type: Sequelize.ENUM('active', 'inactive', 'pending', 'rejected'),
        defaultValue: 'active',
        allowNull: false
      });

      // Add index for status
      await queryInterface.addIndex('rooms', ['status']);
    }

    // Add status column to places table
    const placesTableInfo = await queryInterface.describeTable('places');
    if (!placesTableInfo.status) {
      await queryInterface.addColumn('places', 'status', {
        type: Sequelize.ENUM('active', 'inactive', 'pending', 'rejected'),
        defaultValue: 'active',
        allowNull: false
      });

      // Add index for status
      await queryInterface.addIndex('places', ['status']);
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Remove status column from rooms table
    const roomsTableInfo = await queryInterface.describeTable('rooms');
    if (roomsTableInfo.status) {
      await queryInterface.removeIndex('rooms', ['status']);
      await queryInterface.removeColumn('rooms', 'status');
    }

    // Remove status column from places table
    const placesTableInfo = await queryInterface.describeTable('places');
    if (placesTableInfo.status) {
      await queryInterface.removeIndex('places', ['status']);
      await queryInterface.removeColumn('places', 'status');
    }
  }
};
