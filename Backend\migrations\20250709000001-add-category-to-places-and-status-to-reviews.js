'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add category to places table
    await queryInterface.addColumn('places', 'category', {
      type: Sequelize.ENUM('an_uong', 'leo_nui', 'nghi_duong', 'van_hoa_lich_su', 'giai_tri', 'mua_sam', 'thien_nhien', 'bien_dao', 'chua_den', 'khac'),
      allowNull: false,
      defaultValue: 'khac',
      comment: 'Loại địa điểm: ăn uống, leo núi, nghỉ dưỡng, văn hóa l<PERSON>ch sử, gi<PERSON><PERSON> tr<PERSON>, mua sắm, thiên nhiên, biển đảo, chùa đền, khác'
    });

    // Add status to reviews table
    await queryInterface.addColumn('reviews', 'status', {
      type: Sequelize.ENUM('pending', 'approved', 'rejected'),
      allowNull: false,
      defaultValue: 'pending',
      comment: 'Trạng thái duyệt bài: pending (chờ duyệt), approved (đã duyệt), rejected (từ chối)'
    });

    // Add index for better performance
    await queryInterface.addIndex('places', ['category']);
    await queryInterface.addIndex('reviews', ['status']);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes
    await queryInterface.removeIndex('places', ['category']);
    await queryInterface.removeIndex('reviews', ['status']);
    
    // Remove columns
    await queryInterface.removeColumn('places', 'category');
    await queryInterface.removeColumn('reviews', 'status');
  }
};
