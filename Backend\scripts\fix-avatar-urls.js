const { sequelize } = require('../src/config/database');
const { User } = require('../src/models');

/**
 * <PERSON><PERSON><PERSON> to fix avatar URLs in database
 * This script will:
 * 1. Find all users with avatar_url containing "/uploads/avatars/"
 * 2. Update them to use "/uploads/" instead
 */

const fixAvatarUrls = async () => {
  try {
    console.log('🔄 Starting avatar URL fix...');
    
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');

    // Find users with old avatar paths
    const users = await User.findAll({
      where: {
        avatar_url: {
          [sequelize.Sequelize.Op.like]: '%/uploads/avatars/%'
        }
      }
    });

    console.log(`Found ${users.length} users with old avatar paths`);

    let updated = 0;
    for (const user of users) {
      try {
        const oldUrl = user.avatar_url;
        // Replace /uploads/avatars/ with /uploads/
        const newUrl = oldUrl.replace('/uploads/avatars/', '/uploads/');
        
        await user.update({ avatar_url: newUrl });
        console.log(`✅ Updated user ${user.id}: ${oldUrl} → ${newUrl}`);
        updated++;
      } catch (error) {
        console.error(`❌ Error updating user ${user.id}:`, error.message);
      }
    }

    console.log(`\n🎉 Migration completed!`);
    console.log(`📊 Updated ${updated} out of ${users.length} users`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await sequelize.close();
  }
};

// Dry run function to preview changes
const dryRun = async () => {
  try {
    console.log('🔍 Running dry run (preview mode)...');
    
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');

    const users = await User.findAll({
      where: {
        avatar_url: {
          [sequelize.Sequelize.Op.like]: '%/uploads/avatars/%'
        }
      }
    });

    console.log(`\n👤 Users that would be updated: ${users.length}`);
    users.forEach(user => {
      const oldUrl = user.avatar_url;
      const newUrl = oldUrl.replace('/uploads/avatars/', '/uploads/');
      console.log(`   User ${user.id} (${user.name}): ${oldUrl} → ${newUrl}`);
    });

  } catch (error) {
    console.error('❌ Dry run failed:', error);
  } finally {
    await sequelize.close();
  }
};

// Check command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run') || args.includes('-d');

if (isDryRun) {
  console.log('🔍 Running in dry-run mode (no changes will be made)');
  dryRun();
} else {
  console.log('⚠️  Running in live mode (changes will be made to database)');
  console.log('💡 Use --dry-run flag to preview changes first');
  fixAvatarUrls();
}
