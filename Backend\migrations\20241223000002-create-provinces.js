'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('provinces', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true
      }
    });

    // Add index
    await queryInterface.addIndex('provinces', ['name']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('provinces');
  }
};
