'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add rating statistics columns to reviews table
    await queryInterface.addColumn('reviews', 'average_rating', {
      type: Sequelize.DECIMAL(3, 2),
      defaultValue: 0,
      allowNull: false,
      comment: '<PERSON>iể<PERSON> đ<PERSON>h giá trung bình của bài review'
    });

    await queryInterface.addColumn('reviews', 'total_ratings', {
      type: Sequelize.INTEGER,
      defaultValue: 0,
      allowNull: false,
      comment: 'Tổng số lượt đánh giá bài review'
    });

    // Add index for better performance
    await queryInterface.addIndex('reviews', ['average_rating']);
    await queryInterface.addIndex('reviews', ['total_ratings']);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes
    await queryInterface.removeIndex('reviews', ['average_rating']);
    await queryInterface.removeIndex('reviews', ['total_ratings']);
    
    // Remove columns
    await queryInterface.removeColumn('reviews', 'average_rating');
    await queryInterface.removeColumn('reviews', 'total_ratings');
  }
};
